#pragma once

#include <QObject>
#include <QString>
#include <QStringList>
#include <QJsonObject>
#include <QVariant>
#include <memory>
#include <functional>

namespace DeclarativeUI::HotReload::FormatSupport {

/**
 * @brief Processing result for format-specific operations
 */
struct ProcessingResult {
    bool success = false;
    QString processed_content;
    QString error_message;
    QJsonObject metadata;
    qint64 processing_time_ms = 0;
    
    // Additional data for specific formats
    QVariantMap additional_data;
    
    ProcessingResult() = default;
    ProcessingResult(bool success, const QString& content = QString(), const QString& error = QString())
        : success(success), processed_content(content), error_message(error) {}
    
    static ProcessingResult createSuccess(const QString& content, const QJsonObject& metadata = QJsonObject()) {
        ProcessingResult result(true, content);
        result.metadata = metadata;
        return result;
    }
    
    static ProcessingResult createError(const QString& error) {
        return ProcessingResult(false, QString(), error);
    }
};

/**
 * @brief Configuration for format processing
 */
struct ProcessingConfig {
    bool enable_transpilation = false;
    bool enable_minification = false;
    bool enable_source_maps = false;
    bool enable_live_injection = true;
    QString output_format = "auto";
    QJsonObject custom_options;
    
    // Performance settings
    int max_processing_time_ms = 5000;
    bool enable_caching = true;
    bool enable_parallel_processing = false;
};

/**
 * @brief Base interface for format-specific processors
 */
class IFormatProcessor : public QObject {
    Q_OBJECT

public:
    explicit IFormatProcessor(QObject* parent = nullptr) : QObject(parent) {}
    virtual ~IFormatProcessor() = default;

    // Core interface methods
    virtual QString getFormatName() const = 0;
    virtual QStringList getSupportedExtensions() const = 0;
    virtual bool canProcess(const QString& file_path) const = 0;
    
    // Processing methods
    virtual ProcessingResult processFile(const QString& file_path, const ProcessingConfig& config = ProcessingConfig()) = 0;
    virtual ProcessingResult processContent(const QString& content, const QString& file_path, const ProcessingConfig& config = ProcessingConfig()) = 0;
    
    // Validation methods
    virtual bool validateFile(const QString& file_path) const = 0;
    virtual bool validateContent(const QString& content) const = 0;
    
    // Configuration and capabilities
    virtual ProcessingConfig getDefaultConfig() const { return ProcessingConfig(); }
    virtual QStringList getRequiredDependencies() const { return QStringList(); }
    virtual bool isAvailable() const { return true; }
    
    // Hot reload specific methods
    virtual bool supportsLiveInjection() const { return false; }
    virtual ProcessingResult prepareLiveInjection(const QString& content, const QString& file_path) { 
        Q_UNUSED(content) Q_UNUSED(file_path)
        return ProcessingResult::createError("Live injection not supported"); 
    }
    
    // Caching support
    virtual QString getCacheKey(const QString& file_path, const ProcessingConfig& config) const;
    virtual bool shouldInvalidateCache(const QString& file_path, const QJsonObject& cached_metadata) const;

signals:
    void processingStarted(const QString& file_path);
    void processingFinished(const QString& file_path, const ProcessingResult& result);
    void processingError(const QString& file_path, const QString& error);
    void liveInjectionReady(const QString& file_path, const QString& processed_content);

protected:
    // Helper methods for derived classes
    QString getFileExtension(const QString& file_path) const;
    QString readFileContent(const QString& file_path) const;
    bool writeFileContent(const QString& file_path, const QString& content) const;
    QJsonObject createMetadata(const QString& file_path) const;
    
    // Performance measurement
    void startPerformanceMeasurement();
    qint64 endPerformanceMeasurement();

private:
    QElapsedTimer performance_timer_;
};

/**
 * @brief Factory interface for creating format processors
 */
class IFormatProcessorFactory {
public:
    virtual ~IFormatProcessorFactory() = default;
    virtual std::unique_ptr<IFormatProcessor> createProcessor() const = 0;
    virtual QString getProcessorName() const = 0;
    virtual QStringList getSupportedExtensions() const = 0;
};

/**
 * @brief Template factory implementation
 */
template<typename ProcessorType>
class FormatProcessorFactory : public IFormatProcessorFactory {
public:
    std::unique_ptr<IFormatProcessor> createProcessor() const override {
        return std::make_unique<ProcessorType>();
    }
    
    QString getProcessorName() const override {
        auto temp_processor = std::make_unique<ProcessorType>();
        return temp_processor->getFormatName();
    }
    
    QStringList getSupportedExtensions() const override {
        auto temp_processor = std::make_unique<ProcessorType>();
        return temp_processor->getSupportedExtensions();
    }
};

} // namespace DeclarativeUI::HotReload::FormatSupport
