#include "FormatProcessorRegistry.hpp"
#include <QFileInfo>
#include <QDebug>
#include <QMutexLocker>
#include <QReadLocker>
#include <QWriteLocker>
#include <algorithm>

namespace DeclarativeUI::HotReload::FormatSupport {

FormatProcessorRegistry& FormatProcessorRegistry::instance() {
    static FormatProcessorRegistry instance;
    return instance;
}

FormatProcessorRegistry::FormatProcessorRegistry(QObject* parent) : QObject(parent) {
    registerBuiltinProcessors();
}

void FormatProcessorRegistry::registerProcessor(std::unique_ptr<IFormatProcessorFactory> factory) {
    if (!factory) {
        qWarning() << "Cannot register null processor factory";
        return;
    }
    
    QString format_name = factory->getProcessorName();
    QStringList extensions = factory->getSupportedExtensions();
    
    {
        QWriteLocker locker(&registry_lock_);
        factories_[format_name] = std::move(factory);
    }
    
    updateExtensionMappings();
    
    qDebug() << "🔥 Registered format processor:" << format_name << "for extensions:" << extensions;
    emit processorRegistered(format_name, extensions);
}

std::unique_ptr<IFormatProcessor> FormatProcessorRegistry::createProcessor(const QString& format_name) const {
    QReadLocker locker(&registry_lock_);
    
    auto it = factories_.find(format_name);
    if (it != factories_.end()) {
        return it->second->createProcessor();
    }
    
    qWarning() << "No processor found for format:" << format_name;
    return nullptr;
}

std::unique_ptr<IFormatProcessor> FormatProcessorRegistry::createProcessorForFile(const QString& file_path) const {
    QString extension = getExtensionFromPath(file_path);
    return createProcessorForExtension(extension);
}

std::unique_ptr<IFormatProcessor> FormatProcessorRegistry::createProcessorForExtension(const QString& extension) const {
    QReadLocker locker(&registry_lock_);
    
    auto it = extension_to_processors_.find(extension.toLower());
    if (it != extension_to_processors_.end() && !it->second.isEmpty()) {
        // Use the first processor for this extension
        QString format_name = it->second.first();
        auto factory_it = factories_.find(format_name);
        if (factory_it != factories_.end()) {
            return factory_it->second->createProcessor();
        }
    }
    
    return nullptr;
}

bool FormatProcessorRegistry::hasProcessor(const QString& format_name) const {
    QReadLocker locker(&registry_lock_);
    return factories_.find(format_name) != factories_.end();
}

bool FormatProcessorRegistry::canProcessFile(const QString& file_path) const {
    QString extension = getExtensionFromPath(file_path);
    return canProcessExtension(extension);
}

bool FormatProcessorRegistry::canProcessExtension(const QString& extension) const {
    QReadLocker locker(&registry_lock_);
    return extension_to_processors_.find(extension.toLower()) != extension_to_processors_.end();
}

QStringList FormatProcessorRegistry::getRegisteredFormats() const {
    QReadLocker locker(&registry_lock_);
    QStringList formats;
    for (const auto& pair : factories_) {
        formats.append(pair.first);
    }
    return formats;
}

QStringList FormatProcessorRegistry::getSupportedExtensions() const {
    QReadLocker locker(&registry_lock_);
    QStringList extensions;
    for (const auto& pair : extension_to_processors_) {
        extensions.append(pair.first);
    }
    return extensions;
}

QStringList FormatProcessorRegistry::getProcessorsForExtension(const QString& extension) const {
    QReadLocker locker(&registry_lock_);
    auto it = extension_to_processors_.find(extension.toLower());
    return (it != extension_to_processors_.end()) ? it->second : QStringList();
}

void FormatProcessorRegistry::setDefaultConfig(const QString& format_name, const ProcessingConfig& config) {
    QWriteLocker locker(&registry_lock_);
    default_configs_[format_name] = config;
}

ProcessingConfig FormatProcessorRegistry::getDefaultConfig(const QString& format_name) const {
    QReadLocker locker(&registry_lock_);
    auto it = default_configs_.find(format_name);
    return (it != default_configs_.end()) ? it->second : ProcessingConfig();
}

void FormatProcessorRegistry::enableCaching(bool enabled) {
    QMutexLocker locker(&cache_lock_);
    caching_enabled_ = enabled;
    if (!enabled) {
        cache_.clear();
    }
}

void FormatProcessorRegistry::clearCache() {
    QMutexLocker locker(&cache_lock_);
    cache_.clear();
}

void FormatProcessorRegistry::clearCacheForFormat(const QString& format_name) {
    QMutexLocker locker(&cache_lock_);
    auto it = cache_.begin();
    while (it != cache_.end()) {
        if (it->second.metadata.value("processor").toString() == format_name) {
            it = cache_.erase(it);
        } else {
            ++it;
        }
    }
}

FormatProcessorRegistry::ProcessorStats FormatProcessorRegistry::getStats(const QString& format_name) const {
    QMutexLocker locker(&stats_lock_);
    auto it = stats_.find(format_name);
    return (it != stats_.end()) ? it->second : ProcessorStats{format_name, 0, 0, 0, 0, 0, 0};
}

QList<FormatProcessorRegistry::ProcessorStats> FormatProcessorRegistry::getAllStats() const {
    QMutexLocker locker(&stats_lock_);
    QList<ProcessorStats> result;
    for (const auto& pair : stats_) {
        result.append(pair.second);
    }
    return result;
}

void FormatProcessorRegistry::resetStats() {
    QMutexLocker locker(&stats_lock_);
    stats_.clear();
}

void FormatProcessorRegistry::registerBuiltinProcessors() {
    // Built-in processors will be registered here
    // This will be implemented when we create specific processors
    qDebug() << "🔥 Format processor registry initialized";
}

void FormatProcessorRegistry::updateExtensionMappings() {
    QWriteLocker locker(&registry_lock_);
    extension_to_processors_.clear();
    
    for (const auto& pair : factories_) {
        const QString& format_name = pair.first;
        QStringList extensions = pair.second->getSupportedExtensions();
        
        for (const QString& ext : extensions) {
            QString lower_ext = ext.toLower();
            extension_to_processors_[lower_ext].append(format_name);
        }
    }
}

QString FormatProcessorRegistry::getExtensionFromPath(const QString& file_path) const {
    QFileInfo file_info(file_path);
    return file_info.suffix().toLower();
}

bool FormatProcessorRegistry::getCachedResult(const QString& cache_key, ProcessingResult& result) const {
    QMutexLocker locker(&cache_lock_);

    if (!caching_enabled_) {
        return false;
    }

    auto it = cache_.find(cache_key);
    if (it != cache_.end()) {
        const CacheEntry& entry = it->second;

        // Check if cache entry is still valid
        qint64 age_ms = entry.created_time.msecsTo(QDateTime::currentDateTime());
        if (age_ms <= max_cache_age_ms_) {
            result = ProcessingResult::createSuccess(entry.processed_content, entry.metadata);
            return true;
        } else {
            // Remove expired entry
            const_cast<std::unordered_map<QString, CacheEntry>&>(cache_).erase(it);
        }
    }

    return false;
}

void FormatProcessorRegistry::setCachedResult(const QString& cache_key, const ProcessingResult& result, const QJsonObject& metadata) {
    QMutexLocker locker(&cache_lock_);

    if (!caching_enabled_ || !result.success) {
        return;
    }

    // Clean up cache if it's getting too large
    if (cache_.size() >= max_cache_size_) {
        cleanupExpiredCache();

        // If still too large, remove oldest entries
        if (cache_.size() >= max_cache_size_) {
            auto oldest_it = std::min_element(cache_.begin(), cache_.end(),
                [](const auto& a, const auto& b) {
                    return a.second.created_time < b.second.created_time;
                });
            if (oldest_it != cache_.end()) {
                cache_.erase(oldest_it);
            }
        }
    }

    CacheEntry entry;
    entry.processed_content = result.processed_content;
    entry.metadata = metadata;
    entry.created_time = QDateTime::currentDateTime();
    entry.cache_key = cache_key;

    cache_[cache_key] = std::move(entry);
}

void FormatProcessorRegistry::cleanupExpiredCache() {
    QDateTime current_time = QDateTime::currentDateTime();
    auto it = cache_.begin();

    while (it != cache_.end()) {
        qint64 age_ms = it->second.created_time.msecsTo(current_time);
        if (age_ms > max_cache_age_ms_) {
            it = cache_.erase(it);
        } else {
            ++it;
        }
    }
}

void FormatProcessorRegistry::updateStats(const QString& format_name, qint64 processing_time, bool success, bool cache_hit) const {
    QMutexLocker locker(&stats_lock_);

    ProcessorStats& stats = stats_[format_name];
    stats.format_name = format_name;
    stats.files_processed++;

    if (success) {
        stats.total_processing_time_ms += processing_time;
        stats.average_processing_time_ms = stats.total_processing_time_ms / stats.files_processed;

        if (cache_hit) {
            stats.cache_hits++;
        } else {
            stats.cache_misses++;
        }
    } else {
        stats.errors++;
    }
}

// ProcessorHandle implementation
ProcessorHandle::ProcessorHandle(std::unique_ptr<IFormatProcessor> processor, const QString& format_name)
    : processor_(std::move(processor)), format_name_(format_name) {
    timer_.start();
}

ProcessorHandle::~ProcessorHandle() = default;

ProcessingResult ProcessorHandle::processFile(const QString& file_path, const ProcessingConfig& config) {
    if (!processor_) {
        return ProcessingResult::createError("Invalid processor");
    }

    FormatProcessorRegistry& registry = FormatProcessorRegistry::instance();

    // Check cache first
    QString cache_key = processor_->getCacheKey(file_path, config);
    ProcessingResult result;

    bool cache_hit = registry.getCachedResult(cache_key, result);
    if (cache_hit) {
        registry.updateStats(format_name_, 0, true, true);
        emit registry.cacheHit(format_name_, file_path);
        return result;
    }

    // Process file
    emit registry.processingStarted(format_name_, file_path);

    QElapsedTimer processing_timer;
    processing_timer.start();

    result = processor_->processFile(file_path, config);
    qint64 processing_time = processing_timer.elapsed();

    // Update cache and stats
    if (result.success) {
        registry.setCachedResult(cache_key, result, result.metadata);
    }

    registry.updateStats(format_name_, processing_time, result.success, false);
    emit registry.processingFinished(format_name_, file_path, result.success);
    emit registry.cacheMiss(format_name_, file_path);

    return result;
}

ProcessingResult ProcessorHandle::processContent(const QString& content, const QString& file_path, const ProcessingConfig& config) {
    if (!processor_) {
        return ProcessingResult::createError("Invalid processor");
    }

    FormatProcessorRegistry& registry = FormatProcessorRegistry::instance();
    emit registry.processingStarted(format_name_, file_path);

    QElapsedTimer processing_timer;
    processing_timer.start();

    ProcessingResult result = processor_->processContent(content, file_path, config);
    qint64 processing_time = processing_timer.elapsed();

    registry.updateStats(format_name_, processing_time, result.success, false);
    emit registry.processingFinished(format_name_, file_path, result.success);

    return result;
}

} // namespace DeclarativeUI::HotReload::FormatSupport
