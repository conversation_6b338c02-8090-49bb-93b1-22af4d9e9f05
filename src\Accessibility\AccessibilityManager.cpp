#include "AccessibilityManager.hpp"
#include <QDebug>
#include <QApplication>
#include <QStyle>
#include <QStyleOption>
#include <QTextToSpeech>
#include <QAccessibleEvent>
#include <QFile>
#include <QTextStream>
#include <QJsonDocument>
#include <QJsonObject>

namespace DeclarativeUI::Accessibility {

AccessibilityManager::AccessibilityManager(QObject* parent)
    : QObject(parent)
    , accessibility_enabled_(false)
    , screen_reader_enabled_(false)
    , high_contrast_enabled_(false)
    , keyboard_navigation_enabled_(true)
    , focus_indicator_enabled_(true)
    , voice_announcements_enabled_(false)
    , announcement_timer_(nullptr)
    , focus_frame_(nullptr)
    , last_focused_widget_(nullptr)
    , original_font_size_(QApplication::font().pointSize())
{
    setupGlobalShortcuts();
    setupFocusTracking();
    setupAnnouncementSystem();
    
    // Enable accessibility by default if screen reader is detected
    if (QAccessible::isActive()) {
        enableAccessibility(true);
        enableScreenReader(true);
    }
}

AccessibilityManager& AccessibilityManager::instance() {
    static AccessibilityManager instance_;
    return instance_;
}

void AccessibilityManager::enableAccessibility(bool enable) {
    accessibility_enabled_ = enable;
    
    if (enable) {
        QAccessible::installActivationObserver(this);
        qDebug() << "♿ Accessibility enabled";
    } else {
        QAccessible::removeActivationObserver(this);
        qDebug() << "♿ Accessibility disabled";
    }
    
    emit accessibilityStateChanged(enable);
}

void AccessibilityManager::enableScreenReader(bool enable) {
    screen_reader_enabled_ = enable;
    
    if (enable) {
        // Enhance all existing widgets for screen reader
        for (auto* widget : QApplication::allWidgets()) {
            enhanceForScreenReader(widget);
        }
        qDebug() << "♿ Screen reader support enabled";
    }
}

void AccessibilityManager::enableHighContrast(bool enable) {
    high_contrast_enabled_ = enable;
    
    if (enable) {
        applyHighContrastTheme();
        qDebug() << "♿ High contrast mode enabled";
    } else {
        // Restore original theme
        if (!original_stylesheet_.isEmpty()) {
            QApplication::setStyleSheet(original_stylesheet_);
        }
    }
}

void AccessibilityManager::enableKeyboardNavigation(bool enable) {
    keyboard_navigation_enabled_ = enable;
    
    if (enable) {
        for (auto* widget : QApplication::allWidgets()) {
            applyKeyboardNavigation(widget);
        }
        qDebug() << "♿ Keyboard navigation enabled";
    }
}

void AccessibilityManager::enableFocusIndicator(bool enable) {
    focus_indicator_enabled_ = enable;
    
    if (enable && !focus_frame_) {
        focus_frame_ = new QFocusFrame();
        focus_frame_->setStyleSheet(R"(
            QFocusFrame {
                border: 2px solid #007bff;
                border-radius: 4px;
                background: transparent;
            }
        )");
    } else if (!enable && focus_frame_) {
        focus_frame_->hide();
    }
}

void AccessibilityManager::enableVoiceAnnouncements(bool enable) {
    voice_announcements_enabled_ = enable;
    qDebug() << "♿ Voice announcements" << (enable ? "enabled" : "disabled");
}

void AccessibilityManager::enhanceWidget(QWidget* widget, const AccessibilityAttributes& attributes) {
    if (!widget) return;
    
    widget_attributes_[widget] = attributes;
    applyAccessibilityAttributes(widget, attributes);
    
    // Install custom accessible interface if needed
    if (!attributes.role.isEmpty()) {
        QAccessible::installFactory([widget, attributes](const QString& classname, QObject* object) -> QAccessibleInterface* {
            if (object == widget) {
                return new EnhancedAccessibleWidget(widget, attributes);
            }
            return nullptr;
        });
    }
}

void AccessibilityManager::setAccessibleName(QWidget* widget, const QString& name) {
    if (widget) {
        widget->setAccessibleName(name);
        if (widget_attributes_.find(widget) != widget_attributes_.end()) {
            widget_attributes_[widget].label = name;
        }
    }
}

void AccessibilityManager::setAccessibleDescription(QWidget* widget, const QString& description) {
    if (widget) {
        widget->setAccessibleDescription(description);
        if (widget_attributes_.find(widget) != widget_attributes_.end()) {
            widget_attributes_[widget].description = description;
        }
    }
}

void AccessibilityManager::setAccessibleRole(QWidget* widget, const QString& role) {
    if (widget && widget_attributes_.find(widget) != widget_attributes_.end()) {
        widget_attributes_[widget].role = role;
    }
}

void AccessibilityManager::setAccessibleValue(QWidget* widget, const QString& value) {
    if (widget && widget_attributes_.find(widget) != widget_attributes_.end()) {
        widget_attributes_[widget].value = value;
        
        // Notify screen reader of value change
        if (screen_reader_enabled_) {
            QAccessibleValueChangeEvent event(widget, value);
            QAccessible::updateAccessibility(&event);
        }
    }
}

void AccessibilityManager::setAccessibleHelp(QWidget* widget, const QString& help) {
    if (widget && widget_attributes_.find(widget) != widget_attributes_.end()) {
        widget_attributes_[widget].help = help;
    }
}

void AccessibilityManager::setState(QWidget* widget, const QString& state, bool enabled) {
    if (!widget || widget_attributes_.find(widget) == widget_attributes_.end()) return;
    
    auto& attributes = widget_attributes_[widget];
    
    if (enabled) {
        if (!attributes.states.contains(state)) {
            attributes.states.append(state);
        }
    } else {
        attributes.states.removeAll(state);
    }
    
    // Notify screen reader of state change
    if (screen_reader_enabled_) {
        QAccessibleStateChangeEvent event(widget, QAccessible::State());
        QAccessible::updateAccessibility(&event);
    }
}

void AccessibilityManager::setProperty(QWidget* widget, const QString& property, const QVariant& value) {
    if (!widget || widget_attributes_.find(widget) == widget_attributes_.end()) return;
    
    auto& attributes = widget_attributes_[widget];
    
    // Store property as string representation
    QString prop_string = QString("%1=%2").arg(property, value.toString());
    
    // Remove existing property with same name
    for (int i = attributes.properties.size() - 1; i >= 0; --i) {
        if (attributes.properties[i].startsWith(property + "=")) {
            attributes.properties.removeAt(i);
        }
    }
    
    attributes.properties.append(prop_string);
}

void AccessibilityManager::updateLiveRegion(QWidget* widget, const QString& text) {
    if (!widget || !screen_reader_enabled_) return;
    
    auto it = widget_attributes_.find(widget);
    if (it != widget_attributes_.end() && it->second.live) {
        // Announce live region update
        AnnouncementPriority priority = (it->second.live_politeness == "assertive") ? 
            AnnouncementPriority::High : AnnouncementPriority::Medium;
        announce(text, priority);
        
        // Update accessible value
        setAccessibleValue(widget, text);
    }
}

void AccessibilityManager::announce(const QString& text, AnnouncementPriority priority) {
    if (!voice_announcements_enabled_ || text.isEmpty()) return;
    
    // Add priority prefix for queue management
    QString prioritized_text;
    switch (priority) {
        case AnnouncementPriority::Critical:
            prioritized_text = "[CRITICAL] " + text;
            break;
        case AnnouncementPriority::High:
            prioritized_text = "[HIGH] " + text;
            break;
        case AnnouncementPriority::Medium:
            prioritized_text = "[MEDIUM] " + text;
            break;
        case AnnouncementPriority::Low:
            prioritized_text = "[LOW] " + text;
            break;
    }
    
    // For critical announcements, clear queue and announce immediately
    if (priority == AnnouncementPriority::Critical) {
        announcement_queue_.clear();
        current_announcement_ = text;
        processAnnouncementQueue();
    } else {
        announcement_queue_.append(prioritized_text);
        if (announcement_timer_ && !announcement_timer_->isActive()) {
            announcement_timer_->start();
        }
    }
    
    emit announcementRequested(text, priority);
    qDebug() << "♿ Announcement:" << text;
}

void AccessibilityManager::announceWidgetState(QWidget* widget) {
    if (!widget || !voice_announcements_enabled_) return;
    
    QString description = getWidgetDescription(widget);
    if (!description.isEmpty()) {
        announce(description, AnnouncementPriority::Medium);
    }
}

void AccessibilityManager::announceNavigation(QWidget* from, QWidget* to) {
    Q_UNUSED(from);
    
    if (!to || !voice_announcements_enabled_) return;
    
    QString description = getWidgetDescription(to);
    if (!description.isEmpty()) {
        announce("Focused: " + description, AnnouncementPriority::Low);
    }
}

void AccessibilityManager::applyHighContrastTheme() {
    if (original_stylesheet_.isEmpty()) {
        original_stylesheet_ = QApplication::styleSheet();
    }
    
    QString high_contrast_style = R"(
        QWidget {
            background-color: black;
            color: white;
            border: 1px solid white;
        }
        QPushButton {
            background-color: #333333;
            color: white;
            border: 2px solid white;
            padding: 8px;
        }
        QPushButton:hover {
            background-color: #555555;
        }
        QPushButton:pressed {
            background-color: #777777;
        }
        QLineEdit, QTextEdit {
            background-color: black;
            color: yellow;
            border: 2px solid yellow;
        }
        QLabel {
            color: white;
            background-color: transparent;
        }
        QComboBox {
            background-color: #333333;
            color: white;
            border: 2px solid white;
        }
        QListWidget, QTreeWidget, QTableWidget {
            background-color: black;
            color: white;
            alternate-background-color: #222222;
        }
        QScrollBar {
            background-color: #333333;
            border: 1px solid white;
        }
        QScrollBar::handle {
            background-color: white;
        }
    )";
    
    QApplication::setStyleSheet(high_contrast_style);
}

void AccessibilityManager::applyAccessibleColors(QWidget* widget) {
    if (!widget || !high_contrast_enabled_) return;
    
    // Apply high contrast colors to specific widget
    widget->setStyleSheet(R"(
        background-color: black;
        color: white;
        border: 1px solid white;
    )");
}

void AccessibilityManager::increaseFontSize(int increment) {
    QFont app_font = QApplication::font();
    app_font.setPointSize(app_font.pointSize() + increment);
    QApplication::setFont(app_font);
    
    qDebug() << "♿ Font size increased to" << app_font.pointSize();
}

void AccessibilityManager::resetFontSize() {
    QFont app_font = QApplication::font();
    app_font.setPointSize(original_font_size_);
    QApplication::setFont(app_font);
    
    qDebug() << "♿ Font size reset to" << original_font_size_;
}

bool AccessibilityManager::isAccessibilityEnabled() const {
    return accessibility_enabled_;
}

} // namespace DeclarativeUI::Accessibility
